//------------------------------------------------------------------------
// 简称: ATRMD
// 名称: 
// 类别: 公式应用
// 类型: 用户应用 
// 输出:
//------------------------------------------------------------------------

Params
	Numeric Length(14); 
	Numeric Length2(20); 	
Vars
	Series<Numeric> ATRMD;
	Series<Numeric> ATRZZ;
	Series<Numeric> TR;
	Series<Numeric> ATR;
	Series<Numeric> ATRN;
	Numeric XY;
	Numeric SY;
	Numeric mids;
Events
    onBar(ArrayRef<Integer> indexs)
    {    
    	TR=MAX(MAX((HIGH-LOW),ABS(CLOSE[1]-HIGH)),ABS(CLOSE[1]-LOW));   
    	ATR=Average(TR,Length);
    	ATRMD=(ATR/Close)/Summation((ATR/Close),Length2);
    	ATRN=ATRMD*100;
    	XY=0.04*100;
    	SY=0.06*100;
    	mids=0.05*100;
    	PlotNumeric("ATRN",ATRN);
    	PlotNumeric("XY",XY);
    	PlotNumeric("SY",SY);
    	PlotNumeric("mids",mids);
    	//FileAppend("C:\\Formula.log",""+Text(ATRMD)); 
    }

//------------------------------------------------------------------------
// 编译版本	GS2015.12.25
// 用户版本	2019/06/21 18:51:22
// 版权所有	mujinlong1999
// 更改声明	TradeBlazer Software保留对TradeBlazer平台
//			每一版本的TradeBlazer公式修改和重写的权利
//------------------------------------------------------------------------