{BIAS标准化指标 - 乖离率0-100标准化版本}
N1:6;
N2:12;
N3:24;
P:100;

{计算原始乖离率}
BIAS6原始:=(CLOSE-MA(CLOSE,N1))/MA(CLOSE,N1)*100;
BIAS12原始:=(CLOSE-MA(CLOSE,N2))/MA(CLOSE,N2)*100;
BIAS24原始:=(CLOSE-MA(CLOSE,N3))/MA(CLOSE,N3)*100;

{计算P周期内各乖离率的最高值和最低值}
MAX6:=HHV(BIAS6原始,P);
MIN6:=LLV(BIAS6原始,P);
MAX12:=HHV(BIAS12原始,P);
MIN12:=LLV(BIAS12原始,P);
MAX24:=HHV(BIAS24原始,P);
MIN24:=LLV(BIAS24原始,P);

{0-100标准化乖离率}
BIAS6:(BIAS6原始-MIN6)/(MAX6-MIN6)*100,COLORWHITE,LINETHICK2;
BIAS12:(BIAS12原始-MIN12)/(MAX12-MIN12)*100,COLORYELLOW,LINETHICK1;
BIAS24:(BIAS24原始-MIN24)/(MAX24-MIN24)*100,COLORMAGENTA,LINETHICK1;

{标准化参考线}
超买线:80,COLORRED,LINETHICK1;
强势线:70,COLOR00FFFF,LINETHICK1;
中线:50,COLORBLUE,LINETHICK1;
弱势线:30,COLOR00FFFF,LINETHICK1;
超卖线:20,COLORGREEN,LINETHICK1;

{多周期共振信号}
{三线超买共振}
超买共振:=BIAS6>80 AND BIAS12>80 AND BIAS24>80;
{三线超卖共振}
超卖共振:=BIAS6<20 AND BIAS12<20 AND BIAS24<20;

{黄金交叉和死亡交叉}
金叉:=CROSS(BIAS6,BIAS12) AND BIAS6>50;
死叉:=CROSS(BIAS12,BIAS6) AND BIAS6<50;

{背离分析}
{价格创新高但BIAS6未创新高}
顶背离:=HIGH>=HHV(HIGH,10) AND BIAS6<HHV(BIAS6,10) AND BIAS6>70;
{价格创新低但BIAS6未创新低}
底背离:=LOW<=LLV(LOW,10) AND BIAS6>LLV(BIAS6,10) AND BIAS6<30;

{信号显示}
STICKLINE(超买共振,80,100,8,0),COLORRED;
STICKLINE(超卖共振,0,20,8,0),COLORGREEN;
STICKLINE(金叉,45,55,3,0),COLORYELLOW;
STICKLINE(死叉,45,55,3,0),COLORBLUE;

{背离信号标记}
DRAWICON(顶背离,BIAS6+3,1),COLORRED;
DRAWICON(底背离,BIAS6-3,2),COLORGREEN;

{文字提示}
DRAWTEXT(超买共振,90,'超买'),COLORRED;
DRAWTEXT(超卖共振,10,'超卖'),COLORGREEN;
DRAWTEXT(金叉,BIAS6+5,'金叉'),COLORYELLOW;
DRAWTEXT(死叉,BIAS6-5,'死叉'),COLORBLUE;

{使用说明}
{1. BIAS值在0-100之间波动，50为中性线}
{2. >80：超买区域，考虑减仓}
{3. 70-80：强势区域，持股观望}
{4. 30-70：正常波动区域}
{5. 20-30：弱势区域，关注反弹}
{6. <20：超卖区域，考虑建仓}
{7. 三线共振信号可靠性更高}
{8. 结合背离分析提高准确性}
