#coding:gbk

"""
移动止盈功能配置示例
展示如何在策略中配置和使用移动止盈功能
"""

def configure_trailing_stop_examples():
    """移动止盈配置示例"""
    
    print("="*80)
    print("🎯 移动止盈功能配置示例")
    print("="*80)
    
    print("\n1. 基础配置示例")
    print("-" * 40)
    print("# 在策略初始化后配置移动止盈")
    print("# 启用移动止盈，设置2%基础回撤比例")
    print("set_trailing_stop_config(C, enable=True, ratio=0.02)")
    print()
    print("# 效果：")
    print("# - 低波动区：1%回撤")
    print("# - 正常波动区：2%回撤") 
    print("# - 高波动区：3%回撤")
    print("# - 极高波动区：4%回撤")
    
    print("\n2. 保守型配置（适合大盘蓝筹股）")
    print("-" * 40)
    print("# 使用较小的回撤比例，减少被震出的概率")
    print("set_trailing_stop_config(C, enable=True, ratio=0.015)")
    print()
    print("# 效果：")
    print("# - 低波动区：0.75%回撤")
    print("# - 正常波动区：1.5%回撤")
    print("# - 高波动区：2.25%回撤")
    print("# - 极高波动区：3%回撤")
    
    print("\n3. 激进型配置（适合高波动成长股）")
    print("-" * 40)
    print("# 使用较大的回撤比例，给价格更多波动空间")
    print("set_trailing_stop_config(C, enable=True, ratio=0.03)")
    print()
    print("# 效果：")
    print("# - 低波动区：1.5%回撤")
    print("# - 正常波动区：3%回撤")
    print("# - 高波动区：4.5%回撤")
    print("# - 极高波动区：6%回撤")
    
    print("\n4. 动态调整示例")
    print("-" * 40)
    print("# 根据市场环境动态调整")
    print("if market_condition == '震荡市':")
    print("    # 震荡市使用较小回撤比例")
    print("    set_trailing_stop_config(C, ratio=0.015)")
    print("elif market_condition == '趋势市':")
    print("    # 趋势市使用较大回撤比例")
    print("    set_trailing_stop_config(C, ratio=0.025)")
    
    print("\n5. 禁用移动止盈")
    print("-" * 40)
    print("# 如果只想使用VAE动态止盈，可以禁用移动止盈")
    print("set_trailing_stop_config(C, enable=False)")
    
    print("\n6. 实盘使用建议")
    print("-" * 40)
    print("💡 参数选择建议：")
    print("   - 大盘蓝筹股：0.015-0.02 (1.5%-2%)")
    print("   - 中小盘股：0.02-0.025 (2%-2.5%)")
    print("   - 高波动股：0.025-0.03 (2.5%-3%)")
    print("   - 题材概念股：0.03-0.04 (3%-4%)")
    print()
    print("🔧 调优方法：")
    print("   1. 分析标的历史波动特征")
    print("   2. 回测不同参数的效果")
    print("   3. 从保守参数开始，逐步调整")
    print("   4. 关注胜率和平均收益的平衡")

def show_trailing_stop_workflow():
    """展示移动止盈工作流程"""
    
    print("\n" + "="*80)
    print("🔄 移动止盈工作流程")
    print("="*80)
    
    print("\n步骤1：策略初始化")
    print("-" * 40)
    print("✅ 设置移动止盈参数")
    print("   C.use_trailing_stop = True")
    print("   C.trailing_stop_ratio = 0.02")
    print("   C.highest_price_since_entry = 0")
    print("   C.trailing_stop_price = 0")
    
    print("\n步骤2：开仓时初始化")
    print("-" * 40)
    print("✅ 买入成功后初始化移动止盈变量")
    print("   C.highest_price_since_entry = entry_price")
    print("   C.trailing_stop_price = 0")
    print("   显示：🎯 移动止盈已启用: 回撤比例=2.0%")
    
    print("\n步骤3：持仓期间动态更新")
    print("-" * 40)
    print("✅ 每个K线周期更新移动止盈线")
    print("   1. 更新最高价：highest_price = max(highest_price, current_price)")
    print("   2. 计算动态回撤比例（基于波动率区间）")
    print("   3. 计算新止盈线：new_stop = highest_price * (1 - ratio)")
    print("   4. 单向更新：stop_price = max(stop_price, new_stop)")
    print("   5. 检查触发：if current_price <= stop_price: 卖出")
    
    print("\n步骤4：平仓时重置")
    print("-" * 40)
    print("✅ 卖出后重置移动止盈变量")
    print("   C.highest_price_since_entry = 0")
    print("   C.trailing_stop_price = 0")
    print("   显示：🎯 移动止盈已重置")

def show_integration_with_vae():
    """展示与VAE动态风控的集成"""
    
    print("\n" + "="*80)
    print("🔗 与VAE动态风控的集成")
    print("="*80)
    
    print("\n多层保护机制：")
    print("-" * 40)
    print("1. 🛡️ 固定止损保护（最高优先级）")
    print("   - 触发条件：亏损 >= 0.5%")
    print("   - 作用：兜底保护，防止大额亏损")
    print()
    print("2. 📉 VAE动态止损")
    print("   - 触发条件：亏损 >= 动态止损比例")
    print("   - 作用：基于市场波动的智能止损")
    print()
    print("3. 🎯 移动止盈（新增）")
    print("   - 触发条件：价格跌破移动止盈线")
    print("   - 作用：保护已获得的利润")
    print()
    print("4. 📈 VAE动态止盈")
    print("   - 触发条件：盈利 >= 动态止盈比例")
    print("   - 作用：基于市场波动的目标止盈")
    print()
    print("5. 📊 卖出信号")
    print("   - 触发条件：技术指标卖出信号")
    print("   - 作用：技术面确认退出")
    
    print("\n协同工作原理：")
    print("-" * 40)
    print("💡 移动止盈与VAE动态止盈的区别：")
    print("   - VAE动态止盈：固定目标，达到即退出")
    print("   - 移动止盈：跟随价格上涨，保护利润")
    print()
    print("🎯 最佳实践：")
    print("   - 趋势初期：移动止盈跟随上涨")
    print("   - 趋势中期：移动止盈保护利润")
    print("   - 趋势末期：VAE动态止盈兜底退出")
    print("   - 意外情况：固定止损最终保护")

if __name__ == "__main__":
    configure_trailing_stop_examples()
    show_trailing_stop_workflow()
    show_integration_with_vae()
    
    print("\n" + "="*80)
    print("✅ 移动止盈配置示例完成")
    print("📚 更多信息请参考：移动止盈功能说明.md")
    print("🧪 功能演示请运行：测试\\移动止盈功能演示.py")
    print("="*80)
